#!/usr/bin/env python3
"""
Test script to simulate the exact scenario from the problem description.
"""

import asyncio
import json
import os
import tempfile
import sys

# Add the BASE directory to the path so we can import the modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'BASE'))

from BASE.http.chat import process_context


async def test_real_scenario():
    """Test the exact scenario described in the problem."""
    
    # Create a test file similar to the email.ts file
    test_content = """import { createTransport } from 'nodemailer';
import { config } from './config';

export const emailTransporter = createTransport({
  host: config.email.host,
  port: config.email.port,
  secure: config.email.secure,
  auth: {
    user: config.email.user,
    pass: config.email.password,
  },
});

export const sendEmail = async (to: string, subject: string, html: string) => {
  try {
    const info = await emailTransporter.sendMail({
      from: config.email.from,
      to,
      subject,
      html,
    });
    return info;
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
};
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.ts', delete=False) as f:
        f.write(test_content)
        temp_file_path = f.name
    
    try:
        # Simulate the exact payload structure from the problem description
        ctx = {
            "id": "eb566b445b988f196fe84501428ddab86e8425287491e2a39cb23d04ac06c26e",
            "type": "file", 
            "name": temp_file_path,
            "path": temp_file_path,
            "sub": f"@{temp_file_path}",
            "kbid": None
        }
        
        # Simulate the message content with empty context tags
        message_content = f"""<cm:context>
    <cm:context:name>{temp_file_path}</cm:context:name>
    <cm:context:type>file</cm:context:type>
    <cm:context:id>eb566b445b988f196fe84501428ddab86e8425287491e2a39cb23d04ac06c26e</cm:context:id>
   <cm:context:content></cm:context:content>
</cm:context> what is this file about"""
        
        print("=== Testing Real Scenario ===")
        print(f"Original message content:\n{message_content}\n")
        
        # Test the process_context function
        replacement_content, additional_content = await process_context(ctx, "what is this file about")
        
        print(f"Generated replacement content:\n{replacement_content}\n")
        
        # Now test the replacement logic (simulating what happens in chat_stream)
        ctx_id = ctx.get('id', '')
        start_marker = f'<cm:context:id>{ctx_id}</cm:context:id>'
        
        if start_marker in message_content:
            # Find the context block
            start_pos = message_content.find('<cm:context>')
            if start_pos != -1:
                end_pos = message_content.find('</cm:context>', start_pos)
                if end_pos != -1:
                    context_block = message_content[start_pos:end_pos + len('</cm:context>')]
                    
                    if start_marker in context_block:
                        # Replace this specific context block
                        new_message_content = (
                            message_content[:start_pos] + 
                            replacement_content + 
                            message_content[end_pos + len('</cm:context>'):]
                        )
                        
                        print(f"Final message content after replacement:\n{new_message_content}\n")
                        
                        # Verify the fix worked
                        if "createTransport" in new_message_content and "sendEmail" in new_message_content:
                            print("✅ SUCCESS: File content is properly populated in the context!")
                            print("✅ SUCCESS: The LLM will now receive the actual file content!")
                        else:
                            print("❌ FAILURE: File content was not properly populated!")
                    else:
                        print("❌ FAILURE: Context block does not contain target ID!")
                else:
                    print("❌ FAILURE: Could not find end of context block!")
            else:
                print("❌ FAILURE: Could not find start of context block!")
        else:
            print("❌ FAILURE: Context ID marker not found!")
            
    finally:
        # Clean up the temporary file
        os.unlink(temp_file_path)


if __name__ == "__main__":
    asyncio.run(test_real_scenario())
