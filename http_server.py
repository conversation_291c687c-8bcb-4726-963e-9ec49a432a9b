from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
import os
import uvicorn
from BASE.http.chat import chat_stream
from  BASE.http.watchdog import watchdog_file_delete, watchdog_file_update
from BASE.http.kb.delete_kb import delete_kb_source
from BASE.http.kb.list_kbs import list_knowledge_bases
# from BASE.http.swagger import swagger_list
from BASE.http.auto_actions import auto_actions
import json
from BASE.http.swagger import swagger_stream, swagger_list
from BASE.http.codelens import debug_code, optimize_code, review_code, test_code
from ipc import IPC
from startup_config import save_session, delete_session
ipc_ = IPC.connect()


app = FastAPI(title="CodeMate HTTP API")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)

@app.get("/")
async def root():
    return {"message": "Hello World"}


@app.post("/chat/stream")
async def chat_stream_handler(request: Request):
    print("Received chat request")
    data = await request.json()
    # Extract additional parameters
    session_id = ipc_.get("current_session")

    return StreamingResponse(
        chat_stream(
            messages=data["messages"],
            call_for="chat",
            session_id=session_id,
            is_web_search=data["web_search"],
            mode = data["mode"].upper()
        ),
        media_type="text/event-stream"
    )


@app.post("/swagger/stream")
async def swagger_stream_handler(request: Request):
    data = await request.json()


    print(f"swagger data : {json.dumps(data, indent=2)}") 

    swagger_data = data

    session_id = ipc_.get("current_session")

    return StreamingResponse(
        swagger_stream(
            swagger_data,
        ),
        media_type="text/event-stream"
    )


@app.post("/swagger_list")
async def swagger_list_handler(request: Request):
    data = await request.json()
    print(f"swagger list data : {json.dumps(data, indent=2)}") 
    return JSONResponse(await swagger_list(data))


@app.post("/delete_kb")
async def delete_kb_handler(request: Request):
    data = await request.json()
    kbid = data.get("kbid", "")
    source = data.get("source", "both")

    return JSONResponse(await delete_kb_source(kbid, source=source))



@app.get("/list_kbs")
async def list_kb(request: Request):
    include_cloud = request.query_params.get("include_cloud", "false").lower() == "true"
    session = ipc_.get("current_session")
    if session is None:
        return JSONResponse({"status": "error", "message": "No session found"})

    print(f"include_cloud: {include_cloud}, session: {session}")

    result = await list_knowledge_bases(include_cloud=include_cloud)
    return JSONResponse(result)



# @app.post("/swagger/list")
# async def get_swagger_list(request: Request):
#     data = await request.json()
#     return JSONResponse(await swagger_list(data))


@app.post("/watchdog/file_delete")
async def watchdog_file_delete_handler(request: Request):
    data = await request.json()
    file_path = data.get("file_path", "")
    if not file_path:
        return JSONResponse({"status": "error", "message": "File path not provided"})

    result = await watchdog_file_delete(file_path=file_path)
    return JSONResponse(result)


@app.post("/watchdog/file_update")
async def watchdog_file_update_handler(request: Request):
    data = await request.json()
    file_path = data.get("file_path", "")
    if not file_path:
        return JSONResponse({"status": "error", "message": "File path not provided"})

    result = await watchdog_file_update(file_path=file_path)
    return JSONResponse(result)


@app.post("/auto-actions/{operation}")
async def auto_actions_handler(request: Request, operation: str):
    data = await request.json()
    session_id = ipc_.get("current_session")
    if session_id is None:
        return JSONResponse({"status": "error", "message": "No session found"})
    result = await auto_actions(data, session_id=session_id, operation=operation)
    return JSONResponse(result)



@app.post("/debug/code")
async def debug_code_handler(request: Request):
    data = await request.json()
    session_id = ipc_.get("current_session")
    if session_id is None:
        return JSONResponse({"status": "error", "message": "No session found"})
    # print(f"Received debug code request: {data}")
    result = await debug_code(data, session_id=session_id)
    return JSONResponse(result)


@app.post('/test/code')
async def test_code_handler(request: Request):
    data = await request.json()
    session_id = ipc_.get("current_session")
    if session_id is None:
        return JSONResponse({"status": "error", "message": "No session found"})
    result = await test_code(data, session_id=session_id)
    return JSONResponse(result)


@app.post('/review/code')
async def review_code_handler(request: Request):
    data = await request.json()
    session_id = ipc_.get("current_session")
    if session_id is None:
        return JSONResponse({"status": "error", "message": "No session found"})
    result = await review_code(data, session_id=session_id)
    return JSONResponse(result)

@app.post('/optimize/code')
async def optimize_code_handler(request: Request):
    data = await request.json()
    session_id = ipc_.get("current_session")
    if session_id is None:
        return JSONResponse({"status": "error", "message": "No session found"})
    result = await optimize_code(data, session_id=session_id)
    return JSONResponse(result)


@app.post("/register_meta")
async def register_meta(request: Request):
    data = await request.json()
    session_id = data.get("session_id", "")
    extension_version = data.get("extension_version", "")
    subsystem_version = data.get("subsystem_version", "")

    save_session(session_id)

    if session_id or extension_version or subsystem_version:
        ipc_.set("current_session", session_id)
        ipc_.set("extension_version", extension_version)
        ipc_.set("subsystem_version", subsystem_version)
        return JSONResponse({
            "session_id": session_id,
            "status": "success",
            "message": "Session ID registered successfully"
        })
    else:
        return JSONResponse({
            "session_id": "",
            "status": "error",
            "message": "No session ID provided"
        })


@app.post("/logout")
async def logout(request: Request):
    data = await request.json()
    session_id = data.get("session_id", "")
    current_session = ipc_.get("current_session")

    # If no session ID provided
    if not session_id:
        return JSONResponse({
            "session_id": "",
            "status": "error",
            "message": "No session ID provided"
        })

    # If session ID matches current session
    if current_session == session_id:
        ipc_.set("current_session", None)
        delete_session()
        return JSONResponse({
            "session_id": session_id,
            "status": "logged_out",
            "message": "Session cleared successfully"
        }) 

    # If session ID doesn't match current session
    return JSONResponse({
        "session_id": session_id,
        "status": "not_found",
        "message": "Session ID does not match current session"
    })

if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=45213)