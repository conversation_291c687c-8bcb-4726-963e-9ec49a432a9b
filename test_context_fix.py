#!/usr/bin/env python3
"""
Test script to verify the context processing fix for file content population.
"""

import asyncio
import json
import os
import tempfile
import sys
import re

# Add the BASE directory to the path so we can import the modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'BASE'))

from BASE.http.chat import process_context


async def test_file_context_processing():
    """Test that file context processing correctly populates content."""
    
    # Create a temporary test file
    test_content = """def hello_world():
    print("Hello, World!")
    return "success"

class TestClass:
    def __init__(self):
        self.value = 42
    
    def get_value(self):
        return self.value
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(test_content)
        temp_file_path = f.name
    
    try:
        # Create a context object similar to what would be in the payload
        ctx = {
            "id": "test123",
            "type": "file",
            "name": temp_file_path,
            "path": temp_file_path,
            "sub": f"@{temp_file_path}",
            "kbid": None
        }
        
        print(f"Testing with context: {json.dumps(ctx, indent=2)}")
        
        # Test the process_context function
        replacement_content, additional_content = await process_context(ctx, "what is this file about")
        
        print(f"\nReplacement content length: {len(replacement_content)}")
        print(f"Additional content length: {len(additional_content)}")
        
        # Check if the replacement content contains the actual file content
        if replacement_content:
            print(f"\nReplacement content preview:")
            print(replacement_content[:500] + "..." if len(replacement_content) > 500 else replacement_content)
            
            # Verify that the file content is actually included
            if "def hello_world():" in replacement_content:
                print("\n✅ SUCCESS: File content is properly included in replacement!")
            else:
                print("\n❌ FAILURE: File content is NOT included in replacement!")
                
            # Verify the XML structure
            if "<cm:context:content>" in replacement_content and "</cm:context:content>" in replacement_content:
                print("✅ SUCCESS: XML structure is correct!")
            else:
                print("❌ FAILURE: XML structure is incorrect!")
        else:
            print("\n❌ FAILURE: No replacement content generated!")
            
    finally:
        # Clean up the temporary file
        os.unlink(temp_file_path)


def test_regex_pattern_matching():
    """Test the regex pattern matching logic."""
    
    # Sample message content with different whitespace formatting
    test_message_content = """<cm:context>
    <cm:context:name>c:\\Users\\<USER>\\Desktop\\amul-backend\\src\\config\\main\\email.ts</cm:context:name>
    <cm:context:type>file</cm:context:type>
    <cm:context:id>eb566b445b988f196fe84501428ddab86e8425287491e2a39cb23d04ac06c26e</cm:context:id>
   <cm:context:content></cm:context:content>
</cm:context> what is this file about"""
    
    # Test context
    ctx = {
        "name": "c:\\Users\\<USER>\\Desktop\\amul-backend\\src\\config\\main\\email.ts",
        "type": "file",
        "id": "eb566b445b988f196fe84501428ddab86e8425287491e2a39cb23d04ac06c26e"
    }
    
    # Create the regex pattern (same logic as in the fixed code)
    ctx_name = re.escape(ctx.get('name', ''))
    ctx_type = re.escape(ctx['type'])
    ctx_id = re.escape(ctx.get('id', ''))
    
    context_pattern = (
        r'<cm:context>\s*'
        rf'<cm:context:name>{ctx_name}</cm:context:name>\s*'
        rf'<cm:context:type>{ctx_type}</cm:context:type>\s*'
        rf'<cm:context:id>{ctx_id}</cm:context:id>\s*'
        r'<cm:context:content></cm:context:content>\s*'
        r'</cm:context>'
    )
    
    print("Testing regex pattern matching...")
    print(f"Pattern: {context_pattern}")
    print(f"Test content: {test_message_content}")
    
    # Test if the pattern matches
    match = re.search(context_pattern, test_message_content, re.DOTALL)
    
    if match:
        print("✅ SUCCESS: Regex pattern matches the context marker!")
        print(f"Matched text: {match.group()}")
        
        # Test replacement
        replacement = "<cm:context>\n    <cm:context:name>test.py</cm:context:name>\n    <cm:context:type>file</cm:context:type>\n    <cm:context:id>test123</cm:context:id>\n    <cm:context:content>FILE CONTENT HERE</cm:context:content>\n</cm:context>"
        
        new_content = re.sub(context_pattern, replacement, test_message_content, flags=re.DOTALL)
        print(f"\nReplacement result: {new_content}")
        
        if "FILE CONTENT HERE" in new_content:
            print("✅ SUCCESS: Replacement works correctly!")
        else:
            print("❌ FAILURE: Replacement did not work!")
    else:
        print("❌ FAILURE: Regex pattern does NOT match the context marker!")


if __name__ == "__main__":
    print("=== Testing Context Processing Fix ===\n")
    
    print("1. Testing regex pattern matching...")
    test_regex_pattern_matching()
    
    print("\n" + "="*50 + "\n")
    
    print("2. Testing file context processing...")
    asyncio.run(test_file_context_processing())
